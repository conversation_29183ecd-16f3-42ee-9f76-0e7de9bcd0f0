= Name of the API

Write a brief summary of the functions of this controller.
Take in account the names of classes and methods.

== Model

Describe all POJOs used in this controller.
Use the include directive and tags to select only properties with annotions. 

== Operations

Describe all operations of this controller.
Take in account operations provided through the repository in this controller by _Spring Data REST_.
Create a curl request for each operation from the snippets generated by _Spring RESTDocs_.
Create a sample request for each operation from the snippets generated by _Spring RESTDocs_.
Create a sample response for each operation from the snippets generated by _Spring RESTDocs_.
