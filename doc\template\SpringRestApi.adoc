= Name of the API

Write a brief summary of the functions of this controller.
Take in account the names of classes and methods.

== Model

Describe state transfer objects used in this controller.
Use the include directive and tags to select only relevant properties with their types and annotions. 

== Operations

Describe operations of this controller.
Create a curl request for each operation from the snippets generated by _Spring RESTDocs_.
Create a sample request for each operation from the snippets generated by _Spring RESTDocs_.
Create a sample response for each operation from the snippets generated by _Spring RESTDocs_.
Describe the response codes for each operation.
