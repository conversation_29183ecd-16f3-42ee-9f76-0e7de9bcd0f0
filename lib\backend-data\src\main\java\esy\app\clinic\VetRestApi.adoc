:restdocDir: ../../../../../../build/generated-snippets
= Vet-API

REST-API für die Verwaltung von _Vet_-Objekten.

== Model

== Operations

=== `POST /api/vet`

Die Operation speichert ein neues _Vet_-Objekt.

****

.CURL
include::{restdocDir}/post-api-vet/curl-request.adoc[]

.Request
include::{restdocDir}/post-api-vet/http-request.adoc[]

.Response
include::{restdocDir}/post-api-vet/response-body.adoc[]

****

Die Operation meldet `Created` bzw. den Code 201, wenn das Objekt erfolgreich angelegt wurde.

Die Operation meldet `Bad Request` bzw. den Code 400, wenn die Daten nicht verarbeitet wurden.

Die Operation meldet `Conflict` bzw. den Code 409, wenn die Daten bereits existieren, nicht vollständig oder gültig sind.

=== `PUT /api/vet/{id}`

Die Operation aktualisiert ein existierendes _Vet_-Objekt oder erzeugt ein Neues.

****

.CURL
include::{restdocDir}/put-api-vet/curl-request.adoc[]

.Request
include::{restdocDir}/put-api-vet/http-request.adoc[]

.Response
include::{restdocDir}/put-api-vet/response-body.adoc[]

****

Die Operation meldet `Ok` bzw. den Code 200, wenn das Objekt erfolgreich aktualisiert wurde.

Die Operation meldet `Created` bzw. den Code 201, wenn das Objekt erfolgreich angelegt wurde.

Die Operation meldet `Bad Request` bzw. den Code 400, wenn die Daten nicht verarbeitet wurden.

Die Operation meldet `Conflict` bzw. den Code 409, wenn die Daten nicht vollständig oder gültig sind.

=== `PATCH /api/vet/{id}`

Die Operation aktualisiert ein existierendes _Vet_-Objekt.

****

.CURL
include::{restdocDir}/patch-api-vet-name/curl-request.adoc[]

.Request
include::{restdocDir}/patch-api-vet-name/http-request.adoc[]

.Response
include::{restdocDir}/patch-api-vet-name/response-body.adoc[]

****

Die Operation meldet `Ok` bzw. den Code 200, wenn das Objekt erfolgreich aktualisiert wurde.

Die Operation meldet `Bad Request` bzw. den Code 400, wenn die Daten nicht verarbeitet wurden.

Die Operation meldet `Conflict` bzw. den Code 409, wenn die Daten nicht vollständig oder gültig sind.

=== `GET /api/vet`

Die Operation gibt alle gespeicherten _Vet_-Objekte zurück.

****

.CURL
include::{restdocDir}/get-api-vet/curl-request.adoc[]

.Request
include::{restdocDir}/get-api-vet/http-request.adoc[]

.Response
include::{restdocDir}/get-api-vet/response-body.adoc[]

****

Die Operation meldet `Ok` bzw. den Code 200, wenn die Daten geladen wurden.

=== `GET /api/vet/{id}`

Die Operation zeigt ein gespeichertes _Vet_-Objekt an.

****

.CURL
include::{restdocDir}/get-api-vet-by-id/curl-request.adoc[]

.Request
include::{restdocDir}/get-api-vet-by-id/http-request.adoc[]

.Response
include::{restdocDir}/get-api-vet-by-id/response-body.adoc[]

****

Die Operation meldet `Ok` bzw. den Code 200, wenn die Daten geladen wurden.

Die Operation meldet `Not found` bzw. den Code 404, wenn die Daten nicht existieren.

=== `DELETE /api/vet/{id}`

Die Operation löscht ein gespeichertes _Vet_-Objekt.

****

.CURL
include::{restdocDir}/delete-api-vet/curl-request.adoc[]

.Request
include::{restdocDir}/delete-api-vet/http-request.adoc[]

****

Die Operation meldet `No Content` bzw. den Code 204, wenn die Daten gelöscht wurden.

Die Operation meldet `Not found` bzw. den Code 404, wenn die Daten nicht existieren.
