:restdocDir: ../../lib/backend-data/build/generated-snippets
= Vet-API

REST-API for managing veterinarian entities in the pet clinic application.
It provides standard CRUD operations, query parameters for filtering, and a search endpoint that returns simplified items.

== Model

The main entity of a _Vet_ managed by this controller for persistence.

.Vet entity
[source,java,options="nowrap"]
----
include::../../lib/backend-api/src/main/java/esy/api/clinic/Vet.java[indent=0,tags=properties]
----

The _Vet_ entity contains:

* `name` (String): The veterinarian's name. Required field, cannot be blank.
* `allSkill` (SortedSet<String>): A collection of skills/specializations the veterinarian has.

The entity extends `JsonJpaEntity` which provides:

* `id` (UUID): Unique identifier for the veterinarian.
* `version` (Long): Version number for optimistic locking.

A simplified representation of a _Vet_ for item selection purposes.

.Vet item
[source,java,options="nowrap"]
----
include::../../lib/backend-api/src/main/java/esy/api/clinic/VetItem.java[indent=0,tags=properties]
----

The _VetItem_ contains:

* `value` (UUID): The unique identifier of the veterinarian.
* `text` (String): The display name of the veterinarian.

== Operations

=== `POST /api/vet`

This operation creates a new _Vet_ entity.

****

.CURL
include::{restdocDir}/post-api-vet/curl-request.adoc[]

.Request
include::{restdocDir}/post-api-vet/http-request.adoc[]

.Response
include::{restdocDir}/post-api-vet/response-body.adoc[]

****

The operation reports `Created` or code 201 if the object was successfully created.

The operation reports `Bad Request` or code 400 if the data was not processed.

The operation reports `Conflict` or code 409 if the data already exists, is incomplete, or is invalid.

=== `PUT /api/vet/{id}`

This operation updates an existing _Vet_ entity or creates a new one.

****

.CURL
include::{restdocDir}/put-api-vet/curl-request.adoc[]

.Request
include::{restdocDir}/put-api-vet/http-request.adoc[]

.Response
include::{restdocDir}/put-api-vet/response-body.adoc[]

****

The operation reports `Ok` or code 200 if the object was successfully updated.

The operation reports `Created` or code 201 if the object was successfully created.

The operation reports `Bad Request` or code 400 if the data was not processed.

The operation reports `Conflict` or code 409 if the data is incomplete or is invalid.

=== `PATCH /api/vet/{id}`

This operation partially updates an existing _Vet_ entity.

****

.CURL
include::{restdocDir}/patch-api-vet-name/curl-request.adoc[]

.Request
include::{restdocDir}/patch-api-vet-name/http-request.adoc[]

.Response
include::{restdocDir}/patch-api-vet-name/response-body.adoc[]

****

The operation reports `Ok` or code 200 if the object was successfully updated.

The operation reports `Bad Request` or code 400 if the data was not processed.

The operation reports `Conflict` or code 409 if the data is incomplete or is invalid.

=== `GET /api/vet`

This operation returns all persisted _Vet_ entities.

****

.CURL
include::{restdocDir}/get-api-vet/curl-request.adoc[]

.Request
include::{restdocDir}/get-api-vet/http-request.adoc[]

.Response
include::{restdocDir}/get-api-vet/response-body.adoc[]

****

The operation supports query parameters for filtering:

* `name`: Filter by veterinarian name (supports contains and like operations with %)
* `page`: Page number for pagination (default: 0)
* `size`: Page size for pagination (default: 9999)
* `sort`: Sort criteria (e.g., `name,asc` or `name,desc`)

The operation reports `Ok` or code 200 if the data was successfully loaded.

=== `GET /api/vet/{id}`

This operation returns a single persisted _Vet_ entity.

****

.CURL
include::{restdocDir}/get-api-vet-by-id/curl-request.adoc[]

.Request
include::{restdocDir}/get-api-vet-by-id/http-request.adoc[]

.Response
include::{restdocDir}/get-api-vet-by-id/response-body.adoc[]

****

The operation reports `Ok` or code 200 if the data was successfully loaded.

The operation reports `Not found` or code 404 if the data does not exist.

=== `DELETE /api/vet/{id}`

This operation deletes a single persisted _Vet_ entity.

****

.CURL
include::{restdocDir}/delete-api-vet/curl-request.adoc[]

.Request
include::{restdocDir}/delete-api-vet/http-request.adoc[]

****

The operation reports `No Content` or code 204 if the data was successfully deleted.

The operation reports `Not found` or code 404 if the data does not exist.

=== `GET /api/vet/search/findAllItem`

This operation returns all persisted _Vet_ entities as simplified _VetItem_ objects for selection purposes.
The items are sorted by name in ascending order.

****

.CURL
[source,bash]
----
curl -X GET 'http://localhost:8080/api/vet/search/findAllItem' \
  -H 'Accept: application/json'
----

.Request
[source,http]
----
GET /api/vet/search/findAllItem HTTP/1.1
Host: localhost:8080
Accept: application/json
----

.Response
[source,json]
----
[
  {
    "value": "550e8400-e29b-41d4-a716-446655440000",
    "text": "Dr. Smith"
  },
  {
    "value": "550e8400-e29b-41d4-a716-446655440001",
    "text": "Dr. Johnson"
  }
]
----

****

The operation reports `Ok` or code 200 if the data was successfully loaded.

